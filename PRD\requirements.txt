我需要针对crawl4ai后端，开发一个任务管理中间层，中间层是一个基于FastAPI的微服务，部署在Docker容器中，作为API调用端和Crawl4AI Docker服务之间的中间层。该系统提供批量任务管理、持久化存储、状态跟踪、结果缓存和推送通知功能，确保大规模爬取任务的可靠执行。
该系统会将API调用方原始调用持久化到redis中，并立即向调用方返回批次id供后续查询
系统会使用API调用中的任务配置为任务中每个url创建单个任务请求，存储在redis中，方便后续发送给crawl4ai后端
系统会将redis中的单个url任务按照任务优先级，按照high、medium、low、retry的绝对优先级顺序进行处理
系统使用XREADGROUP配合redis streams消息队列管理任务，追踪任务全生命周期
任务管理中间层可以配置多个crawl4ai后端，因此当任务从消息队列进入执行状态后，需要记录任务发送给了哪个crawl4ai后端，因为后端id+jobid才能确认后续这个任务要从哪个后端轮询获得状态和结果
任务管理中间层会主动维护crawl4ai后端负载状态，每次向crawl4ai后端推送任务进入processing状态，并获得对应crawl4ai后端返回的jobid时，就记录这个crawl4ai后端负载为1，当任务完成或失败或提交失败时，则这个crawl4ai后端负载-1
因此分配任务时，需要使用redis lua原子化操作保证分配任务的原子性，防止后端load维护错误或分配了超过后端承载能力的任务
任务分配给crawl4ai后端后，系统会持续向对应后端轮询对应job id任务的完成状态，并将最终的结果存入redis，以便API调用方进行查询
每间一段时间，系统会向回调url推送批次任务状态
我需要任务的全过程有日志可追踪，所有请求都进入redis持久化，方便后续调试、跟踪
我需要中间层有基础的告警信息，比如后端不可用，任务循环排队、redis存储过大之类的
我需要可以使用API对 redis streams中的任务队列进行顺序调整
我需要可以使用API管理任务的状态
我需要可以使用API配置多个crawl4ai后端配置，并检查crawl4ai后端状态、管理后端启用情况
我需要系统有对应的webui用于可视化观察中间层系统的运行状态、调整已有任务优先级、访问任务结果等信息、也可以在webui手动创建新任务、可以使用webui管理多crwal4ai后端配置
