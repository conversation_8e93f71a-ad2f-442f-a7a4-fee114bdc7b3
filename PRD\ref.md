# Task-Middleware批次任务处理流程详细分析

## 概述
任务管理中间层是一个基于FastAPI的微服务，部署在Docker容器中，作为API调用端和Crawl4AI Docker服务之间的中间层。该系统提供批量任务管理、持久化存储、状态跟踪、结果缓存和推送通知功能，确保大规模爬取任务的可靠执行。

本文档详细分析 task-middleware 中 `/queue/tasks/batch` 端点的完整处理流程，从API调用到最终任务执行的每个步骤和方法调用。这是基于严格优先级调度和XREADGROUP消费者组机制的现代化任务处理架构。


## 2. 完整处理流程图

### Task-Middleware批次任务处理架构 (实际实现)

```
外部API调用 POST /queue/tasks/batch
    ↓
FastAPI Router (app/api/routes/queue_tasks.py)
    ↓
submit_batch_tasks_to_queue() 函数
    ↓
1. 请求验证和预处理 ✅
    ├─ _validate_queue_request() - 验证请求参数
    │   ├─ URLs数量检查 (1-1000个)
    │   ├─ LLM查询指令验证 (非空，最大10000字符)
    │   └─ 参数格式验证
    ├─ _validate_and_categorize_urls() - URL验证分类
    │   ├─ URL格式验证 (http/https协议)
    │   ├─ 域名有效性检查 (_is_valid_url函数)
    │   └─ 分离有效/无效URL列表
    ├─ _extract_caller_info() - 调用方信息收集
    │   ├─ 客户端IP提取 (支持x-forwarded-for, x-real-ip代理头)
    │   ├─ User-Agent和Referer收集
    │   ├─ 请求ID生成 (req_{timestamp}格式)
    │   └─ 会话追踪信息 (x-session-id)
    └─ _create_valid_request() - 创建有效请求对象
    ↓
2. TaskProducer批次任务创建 ✅
    └─ task_producer.create_batch_tasks_simple()
        ├─ 🔧 批次初始化
        │   ├─ 批次ID生成：simple_batch_{YYYYMMDD_HHMMSS}_{uuid8}
        │   ├─ 优先级队列映射获取 (high/medium/low → queue:priority:*)
        │   ├─ 当前时间戳记录 (datetime.utcnow())
        │   └─ 任务计数器初始化 (task_ids[], created_tasks[])
        │
        ├─ 🔄 批量任务循环创建
        │   ├─ 为每个URL生成唯一任务ID
        │   │   └─ 格式：stask_{YYYYMMDD_HHMMSS}_{uuid8}
        │   ├─ 构建任务消息对象
        │   │   ├─ task_id: 任务唯一标识
        │   │   ├─ batch_id: 批次ID关联
        │   │   ├─ url: 目标URL
        │   │   ├─ config: 任务配置 (包含q, schema_str, cache, callback_url)
        │   │   ├─ priority: 任务优先级
        │   │   ├─ created_at: 创建时间戳 (ISO格式)
        │   │   ├─ caller_info: 调用方追踪信息
        │   │   ├─ sequence: 批次内序号 (i)
        │   │   ├─ retry_count: 初始重试次数 (0)
        │   │   └─ max_retries: 最大重试次数 (3)
        │   │
        │   ├─ 🚀 Redis Streams消息队列发布
        │   │   ├─ 队列选择：self.priority_queues[priority] → queue:priority:{priority}
        │   │   ├─ XADD操作：await redis.xadd(queue_name, task_message)
        │   │   ├─ 消息ID获取：message_id (Redis自动生成)
        │   │   └─ 错误处理：单个任务失败不影响批次继续
        │   │
        │   └─ 📊 任务创建统计
        │       ├─ task_ids.append(task_id) - 成功任务ID收集
        │       ├─ created_tasks.append() - 任务详情记录
        │       │   ├─ task_id: 任务ID
        │       │   ├─ url: 目标URL
        │       │   ├─ message_id: Redis消息ID
        │       │   └─ queue: 队列名称
        │       └─ 调试日志：任务创建和排队状态
        │
        ├─ 💾 Redis Hash任务记录存储 (_store_task_record)
        │   ├─ 任务记录构建
        │   │   ├─ 基础任务数据复制 (**task_data)
        │   │   ├─ message_id: Redis消息ID
        │   │   ├─ queue_name: 队列名称
        │   │   ├─ status: "QUEUED" (初始状态)
        │   │   ├─ queued_at: 排队时间戳
        │   │   └─ 预留字段初始化 (backend_id, worker_id, result等为空)
        │   ├─ 🔄 复杂字段JSON序列化
        │   │   ├─ config字段：JSON.dumps(task_config)
        │   │   ├─ caller_info字段：JSON.dumps(caller_info)
        │   │   └─ result字段：预留为空字符串
        │   ├─ 📝 Redis Hash存储
        │   │   ├─ 键名：task:{task_id}
        │   │   ├─ 数据：HSET操作存储所有字段
        │   │   └─ 过期时间：7天 (86400 * 7秒)
        │   └─ 🔍 字符串类型转换确保Redis兼容性
        │
        ├─ 📦 批次信息Redis存储
        │   ├─ 批次元数据构建
        │   │   ├─ batch_id: 批次唯一标识
        │   │   ├─ batch_name: 用户指定批次名称
        │   │   ├─ total_tasks: 总任务数 (len(urls))
        │   │   ├─ created_tasks: 成功创建任务数
        │   │   ├─ failed_tasks: 失败任务数
        │   │   ├─ priority: 批次优先级
        │   │   ├─ queue_name: 使用的队列名称
        │   │   ├─ created_at: 批次创建时间
        │   │   ├─ caller_info: 调用方信息
        │   │   └─ status: "created" (批次状态)
        │   └─ Redis存储：HSET batch:{batch_id} mapping=batch_info
        │
        ├─ 📈 队列统计更新 (_update_queue_stats)
        │   ├─ 统计键：queue_stats:{queue_name}
        │   ├─ 任务计数递增：HINCRBY total_tasks +created_count
        │   ├─ 更新时间戳：HSET last_updated current_time
        │   ├─ 队列长度查询：XLEN queue_name
        │   └─ 当前长度更新：HSET current_length queue_length
        │
        └─ 🎯 批次结果返回
            ├─ batch_id: 批次唯一标识
            ├─ task_ids[]: 成功创建的任务ID列表
            ├─ created_tasks[]: 任务详情列表 (task_id, url, message_id, queue)
            ├─ total_tasks: 总任务数
            ├─ successful_tasks: 成功创建任务数
            ├─ failed_tasks: 失败任务数
            ├─ priority: 任务优先级
            ├─ queue_name: 使用的Redis队列名称
            └─ estimated_completion_time: 预计完成时间 (_estimate_completion_time)
                ├─ 时间估算算法：task_count * base_time_per_task[priority]
                ├─ 优先级时间基数：high=10s, medium=15s, low=20s
                └─ 返回格式：ISO时间戳
    ↓
3. 负载均衡器状态获取 ✅ (已修复)
    └─ 🔧 修复后的实际处理流程
        ├─ 📋 API路由调用：load_balancer.get_backend_stats()
        │   ├─ submit_batch_tasks_to_queue()：backend_stats = await load_balancer.get_backend_stats()
        │   ├─ get_queue_system_status()：backend_stats = await load_balancer.get_backend_stats()
        │   └─ get_worker_status()：backend_stats = await load_balancer.get_backend_stats()
        │
        ├─ ✅ IntelligentLoadBalancer.get_backend_stats() 实际实现
        │   ├─ 遍历所有配置的后端 (self.backends.items())
        │   ├─ Redis数据获取
        │   │   ├─ 后端负载：backend_load:{backend_id}
        │   │   ├─ 响应时间：backend_response_time:{backend_id}
        │   │   ├─ 成功率：backend_success_rate:{backend_id}
        │   │   └─ 健康状态：health_checker.is_backend_healthy()
        │   └─ 返回后端统计字典
        │       ├─ current_load: int - 当前负载数
        │       ├─ max_capacity: int - 最大容量
        │       ├─ utilization: float - 利用率 (current_load/max_capacity)
        │       ├─ avg_response_time: float - 平均响应时间
        │       ├─ success_rate: float - 成功率
        │       ├─ is_healthy: bool - 健康状态
        │       └─ base_url: str - 后端基础URL
        │
        ├─ 🔄 后端统计到负载均衡器状态转换
        │   ├─ 总容量计算：sum(stats.max_capacity for all backends)
        │   ├─ 当前负载计算：sum(stats.current_load for all backends)
        │   ├─ 活跃后端计数：count(stats.is_healthy == True)
        │   ├─ 利用率计算：current_load / total_capacity
        │   └─ 构建负载均衡器状态对象
        │       ├─ active_workers: int - 活跃后端数量
        │       ├─ total_capacity: int - 总容量
        │       ├─ current_load: int - 当前总负载
        │       ├─ utilization: float - 整体利用率
        │       └─ backend_stats: Dict - 详细后端统计
        │
        ├─ 🎯 Worker详情格式转换 (get_worker_status接口)
        │   ├─ 遍历后端统计数据 (backend_stats.items())
        │   ├─ 构建Worker详情对象
        │   │   ├─ worker_id: str - 使用backend_id作为worker_id
            │   ├─ backend_id: str - 后端标识
            │   ├─ base_url: str - 后端URL
            │   ├─ current_load: int - 当前负载
            │   ├─ max_capacity: int - 最大容量
            │   ├─ utilization: float - 利用率
            │   ├─ avg_response_time: float - 平均响应时间
            │   ├─ success_rate: float - 成功率
            │   └─ is_healthy: bool - 健康状态
            └─ 保持API兼容性和负载均衡算法信息
    ↓
4. 响应构建和返回 ✅ (基于实际代码核实)
    └─ QueueTaskSubmissionResponse构建 (queue_tasks.py第248-268行)
        ├─ 🔧 响应模型定义 (QueueTaskSubmissionResponse类)
        │   ├─ success: bool - 提交是否成功 (Field(..., description="提交是否成功"))
        │   ├─ batch_id: str - 批次ID (Field(..., description="批次ID"))
        │   ├─ task_ids: List[str] - 任务ID列表 (Field(..., description="任务ID列表"))
        │   ├─ total_tasks: int - 总任务数 (Field(..., description="总任务数"))
        │   ├─ valid_tasks: int - 有效任务数 (Field(..., description="有效任务数"))
        │   ├─ invalid_tasks: int - 无效任务数 (Field(..., description="无效任务数"))
        │   ├─ invalid_urls: List[str] - 无效URL列表 (Field(default_factory=list, description="无效URL列表"))
        │   ├─ queue_info: Dict[str, Any] - 队列信息 (Field(..., description="队列信息"))
        │   ├─ load_balancer_info: Dict[str, Any] - 负载均衡信息 (Field(..., description="负载均衡信息"))
        │   ├─ caller_info: Dict[str, Any] - 调用方信息 (Field(..., description="调用方信息"))
        │   ├─ created_at: str - 创建时间 (Field(..., description="创建时间"))
        │   └─ message: str - 响应消息 (Field(..., description="响应消息"))
        │
        ├─ 📊 queue_info构建 (第230-237行)
        │   ├─ batch_id: batch_result.get("batch_id") - 从TaskProducer返回的批次ID
        │   ├─ task_count: len(valid_urls) - 有效URL数量
        │   ├─ invalid_urls: invalid_urls - 无效URL列表
        │   ├─ priority: request.priority.value if request.priority else "medium" - 任务优先级
        │   ├─ routing_strategy: "intelligent_load_balanced" - 固定路由策略
        │   └─ estimated_completion_time: batch_result.get("estimated_completion_time") - 预计完成时间
        │
        ├─ ⚖️ load_balancer_info构建 (第218-225行)
        │   ├─ active_workers: active_backends - 活跃后端数量
        │   ├─ total_capacity: total_capacity - 总容量 (所有后端max_capacity之和)
        │   ├─ current_load: current_load - 当前负载 (所有后端current_load之和)
        │   ├─ utilization: current_load / total_capacity - 利用率计算
        │   └─ backend_stats: backend_stats - 详细后端统计信息
        │
        ├─ 📞 caller_info构建 (_extract_caller_info函数，第38-56行)
        │   ├─ client_ip: 客户端IP地址 (支持x-forwarded-for, x-real-ip代理头)
        │   ├─ user_agent: request.headers.get("user-agent") - 用户代理信息
        │   ├─ referer: request.headers.get("referer") - 来源页面
        │   ├─ origin: request.headers.get("origin") - 请求来源
        │   ├─ x_forwarded_for: request.headers.get("x-forwarded-for") - 代理转发信息
        │   ├─ x_real_ip: request.headers.get("x-real-ip") - 真实IP
        │   ├─ request_id: 请求唯一标识 (x-request-id或自动生成req_{timestamp})
        │   ├─ session_id: request.headers.get("x-session-id") - 会话ID
        │   └─ collected_at: datetime.utcnow().isoformat() - 信息收集时间
        │
        ├─ 🎯 最终响应对象构建 (第240-252行)
        │   ├─ success: True - 固定成功标识
        │   ├─ batch_id: batch_result.get("batch_id", "unknown") - 批次ID (带默认值)
        │   ├─ task_ids: batch_result.get("task_ids", []) - 任务ID列表 (带默认值)
        │   ├─ total_tasks: len(request.urls) - 原始请求URL总数
        │   ├─ valid_tasks: len(valid_urls) - 验证通过的URL数量
        │   ├─ invalid_tasks: len(invalid_urls) - 验证失败的URL数量
        │   ├─ invalid_urls: invalid_urls - 无效URL列表
        │   ├─ queue_info: queue_info - 队列信息对象
        │   ├─ load_balancer_info: load_balancer_status - 负载均衡状态
        │   ├─ caller_info: caller_info - 调用方追踪信息
        │   ├─ created_at: datetime.utcnow().isoformat() - 响应创建时间戳
        │   └─ message: f"Successfully submitted {len(valid_urls)} tasks to queue system with caller tracking" - 动态成功消息
        │
        └─ 📝 响应日志记录 (第254-261行)
            ├─ batch_id: response.batch_id - 批次ID
            ├─ total_tasks: response.total_tasks - 总任务数
            ├─ valid_tasks: response.valid_tasks - 有效任务数
            ├─ invalid_tasks: response.invalid_tasks - 无效任务数
            ├─ client_ip: caller_info.get("client_ip") - 客户端IP
            └─ 日志级别: logger.info - 成功提交信息记录
    ↓
5. 后台任务处理启动 (异步执行) ✅ (基于实际代码核实)
    ├─ TaskWorker启动 (完全委托模式) - task_worker.py
    │   ├─ TaskWorker.start() - 轻量级包装器入口 ✅ (第47-78行)
    │   │   ├─ 🔧 初始化和状态设置
    │   │   │   ├─ self.is_running = True - 启动标识
    │   │   │   ├─ self.start_time = time.time() - 记录启动时间
    │   │   │   └─ 日志记录：🚀 Starting TaskWorker {worker_id} with XREADGROUP support
    │   │   │
    │   │   ├─ 📊 _register_worker_with_monitoring() - 增强Worker注册 (第80-130行)
    │   │   │   ├─ 基础Worker状态注册 (worker_status:{worker_id})
    │   │   │   │   ├─ worker_type: "task_worker_xreadgroup" - XREADGROUP版本标识
    │   │   │   │   ├─ status: "ACTIVE" - 活跃状态
    │   │   │   │   ├─ xreadgroup_enabled: "true" - XREADGROUP功能标识
    │   │   │   │   ├─ consumer_groups: "high_priority_workers,medium_priority_workers,low_priority_workers,retry_workers"
    │   │   │   │   └─ 基础统计信息：processed_count, failed_count, current_tasks
    │   │   │   ├─ 活跃Worker集合管理 (active_workers)
    │   │   │   │   └─ await self.redis.sadd("active_workers", self.worker_id)
    │   │   │   └─ 增强Worker能力注册 (enhanced_worker_registry:{worker_id})
    │   │   │       ├─ supported_priorities: "high,medium,low,retry"
    │   │   │       ├─ max_concurrent_tasks: SYSTEM_CONFIG配置值
    │   │   │       ├─ supports_batch_processing: "true"
    │   │   │       ├─ supports_retry_mechanism: "true"
    │   │   │       ├─ xreadgroup_consumer_groups: "4" - 支持4个消费者组
    │   │   │       └─ load_balancer_integration: "true"
    │   │   │
    │   │   ├─ ⚖️ _create_default_load_balancer() - 创建默认负载均衡器 (第132-180行)
    │   │   │   ├─ 配置源：DEFAULT_BACKEND_CONFIG.fallback_backends
    │   │   │   ├─ 安全配置解析循环
    │   │   │   │   ├─ backend_id: str(config.get("id")) 或自动生成
    │   │   │   │   ├─ base_url: str(config.get("base_url")) 或默认localhost:8000
    │   │   │   │   ├─ max_capacity: config.config.max_concurrent_tasks 或默认10
    │   │   │   │   └─ 创建Backend对象并添加到列表
    │   │   │   ├─ 后端字典构建：{backend.backend_id: backend}
    │   │   │   ├─ IntelligentLoadBalancer实例创建
    │   │   │   │   ├─ redis_client: self.redis
    │   │   │   │   └─ backend_config: backends_dict
    │   │   │   └─ 日志记录：✅ Created load balancer with {count} backends
    │   │   │
    │   │   └─ 🔄 完全委托给StrictPriorityScheduler ✅ (第60-77行)
    │   │       ├─ 导入：from .strict_priority_scheduler import StrictPriorityScheduler
    │   │       ├─ 实例创建：StrictPriorityScheduler(redis_client, load_balancer, worker_id)
    │   │       ├─ 传递参数
    │   │       │   ├─ redis_client: self.redis - Redis连接
    │   │       │   ├─ load_balancer: self.load_balancer - 负载均衡器
    │   │       │   └─ worker_id: self.worker_id - Worker ID用于消费者组命名
    │   │       ├─ 日志记录：🔄 TaskWorker delegating to StrictPriorityScheduler with XREADGROUP
    │   │       ├─ 优先级说明：📊 Priority: HIGH → MEDIUM → LOW → RETRY (strict ordering)
    │   │       └─ 启动调度器：await scheduler.start()
    │
    ├─ StrictPriorityScheduler.start() - 严格优先级调度器启动 ✅ (strict_priority_scheduler.py第67-88行)
    │   ├─ 🔧 调度器初始化
    │   │   ├─ self.is_running = True - 运行状态标识
    │   │   ├─ self.start_time = time.time() - 启动时间记录
    │   │   ├─ worker_id: worker_id 或自动生成 f"scheduler-{uuid8}"
    │   │   ├─ TaskLifecycleTracker集成 - 任务生命周期追踪器
    │   │   └─ 日志记录：🚀 Starting Strict Priority Scheduler {worker_id}
    │   │
    │   ├─ 📊 优先级队列映射配置 (第45-51行)
    │   │   ├─ TaskPriority.HIGH.value: "queue:priority:high"
    │   │   ├─ TaskPriority.MEDIUM.value: "queue:priority:medium"
    │   │   ├─ TaskPriority.LOW.value: "queue:priority:low"
    │   │   └─ "retry": "queue:retry" - retry优先级最低
    │   │
    │   ├─ 🚫 饥饿保护配置 (第53-59行)
    │   │   ├─ starvation_protection_enabled: False - 关闭饥饿保护
    │   │   ├─ starvation_counters: 保留但不使用
    │   │   ├─ max_starvation_count: 20 - 保留配置
    │   │   └─ 实现严格优先级：HIGH绝对优先于MEDIUM，MEDIUM绝对优先于LOW
    │   │
    │   ├─ 📋 _register_worker() - Worker监控注册 (第90-115行)
    │   │   ├─ worker_type: "strict_priority_scheduler" - 调度器类型标识
    │   │   ├─ 基础Worker信息存储 (worker_status:{worker_id})
    │   │   │   ├─ status: "ACTIVE" - 活跃状态
    │   │   │   ├─ start_time: 启动时间戳
    │   │   │   ├─ processed_count: "0" - 初始处理计数
    │   │   │   ├─ failed_count: "0" - 初始失败计数
    │   │   │   └─ last_heartbeat: 当前时间戳
    │   │   ├─ 活跃Worker集合管理
    │   │   │   └─ await self.redis.sadd("active_workers", self.worker_id)
    │   │   └─ 日志记录：✅ Worker {worker_id} registered
    │   │
    │   └─ 🚀 启动3个并发协程 (第75-85行)
    │       ├─ asyncio.create_task(_main_scheduling_loop()) - 主调度循环
    │       ├─ asyncio.create_task(_health_monitor()) - 健康监控
    │       ├─ asyncio.create_task(_heartbeat_loop()) - 心跳循环
    │       ├─ 并发执行：await asyncio.gather(*tasks)
    │       └─ 异常处理和资源清理
    │           ├─ except Exception: 错误日志记录
    │           ├─ finally: await self._unregister_worker()
    │           └─ self.is_running = False
    ↓
6. 严格优先级任务调度 (StrictPriorityScheduler核心逻辑) ✅ (基于实际代码核实)
    ├─ _main_scheduling_loop() - 主调度循环 ✅ (第158-185行)
    │   ├─ 🔄 循环条件：while self.is_running
    │   ├─ 🏥 获取可用后端 (_get_available_backends) ✅ (第187-200行)
    │   │   ├─ 优先方案：检查负载均衡器健康检查器
    │   │   │   └─ await self.load_balancer.health_checker.get_healthy_backends()
    │   │   ├─ 备用方案：返回所有配置的后端
    │   │   │   └─ list(self.load_balancer.backends.values())
    │   │   ├─ 异常处理：返回空列表确保系统稳定性
    │   │   └─ 无后端时：await asyncio.sleep(0.5) 等待重试
    │   │
    │   ├─ 🎯 严格按优先级获取任务 (_get_tasks_by_strict_priority) ✅ (第202-235行)
    │   │   ├─ 🔑 绝对优先级顺序：HIGH → MEDIUM → LOW → RETRY ✅
    │   │   │   ├─ priority_order = [TaskPriority.HIGH.value, TaskPriority.MEDIUM.value, TaskPriority.LOW.value, "retry"]
    │   │   │   ├─ 队列空检查：await self._is_queue_empty(queue_name) 支持XREADGROUP
    │   │   │   ├─ 🔑 关键原则：找到任务后立即返回，不检查低优先级队列
    │   │   │   └─ 日志记录：Found {count} tasks in {priority} priority, skipping lower priorities
    │   │   │
    │   │   ├─ 🚫 饥饿保护机制：已关闭 (starvation_protection_enabled: False) ✅ (第53-59行)
    │   │   │   ├─ 实现严格优先级，高优先级任务绝对优先
    │   │   │   ├─ starvation_counters保留但不使用
    │   │   │   └─ max_starvation_count: 20 - 保留配置
    │   │   │
    │   │   ├─ 📨 XREADGROUP任务获取：_fetch_tasks_from_queue_with_consumer_group() ✅ (第237-320行)
    │   │   │   ├─ 🔧 消费者组配置和确保存在 (_ensure_consumer_group_exists) (第322-350行)
    │   │   │   │   ├─ 消费者组映射：high_priority_workers, medium_priority_workers, low_priority_workers, retry_workers
    │   │   │   │   ├─ 检查现有消费者组：await self.redis.xinfo_groups(queue_name)
    │   │   │   │   ├─ 创建消费者组：await self.redis.xgroup_create(queue_name, consumer_group, id='$', mkstream=True)
    │   │   │   │   └─ BUSYGROUP错误处理：组已存在时忽略
    │   │   │   │
    │   │   │   ├─ 🔄 PENDING消息恢复 (_claim_pending_messages) ✅ (第352-430行) - 新增优化功能
    │   │   │   │   ├─ 获取PENDING消息列表：await self.redis.xpending_range(queue_name, consumer_group)
    │   │   │   │   ├─ 超时检查：5分钟超时阈值 (timeout_threshold = 5 * 60 * 1000)
    │   │   │   │   ├─ 声明超时消息：await self.redis.xclaim(queue_name, consumer_group, consumer_name, min_idle_time, message_ids)
    │   │   │   │   ├─ 处理声明的消息：反序列化、验证、元数据增强
    │   │   │   │   └─ 无效消息ACK：await self.redis.xack(queue_name, consumer_group, message_id)
    │   │   │   │
    │   │   │   ├─ 📥 新消息读取：XREADGROUP (第280-320行)
    │   │   │   │   ├─ 消费者命名：f"{self.worker_id}-{priority}"
    │   │   │   │   ├─ 读取参数：count=remaining_count, block=50ms (减少阻塞时间)
    │   │   │   │   ├─ 消息处理：await self.redis.xreadgroup(consumer_group, consumer_name, {queue_name: '>'})
    │   │   │   │   └─ 异常消息ACK：处理失败的消息直接确认避免重复
    │   │   │   │
    │   │   │   ├─ 🔧 任务数据反序列化 (_deserialize_task_fields) ✅ (第432-470行) - 新增方法
    │   │   │   │   ├─ JSON字段反序列化：["config", "result", "metadata"]
    │   │   │   │   ├─ 数值字段转换：["retry_count", "max_retries", "batch_index", "priority"]
    │   │   │   │   ├─ 时间戳字段转换：["created_at", "queued_at", "processing_started_at", "completed_at", "failed_at"]
    │   │   │   │   └─ 异常处理：字段转换失败时使用默认值
    │   │   │   │
    │   │   │   ├─ ✅ 任务有效性验证 (_validate_task_data) ✅ (第472-505行) - 新增方法
    │   │   │   │   ├─ 必需字段检查：["task_id", "url"]
    │   │   │   │   ├─ URL格式验证：url.startswith(("http://", "https://"))
    │   │   │   │   ├─ 任务ID格式检查：len(task_id) >= 10
    │   │   │   │   ├─ 重试次数验证：retry_count <= max_retries
    │   │   │   │   └─ 返回布尔值：True/False
    │   │   │   │
    │   │   │   └─ 📋 消息元数据增强 (第285-295行)
    │   │   │       ├─ _message_id: Redis消息ID
    │   │   │       ├─ _consumer_group: 消费者组名称
    │   │   │       ├─ _queue_name: 队列名称
    │   │   │       ├─ _consumer_name: 消费者名称
    │   │   │       ├─ _claimed_at: 声明时间戳
    │   │   │       ├─ _priority: 任务优先级
    │   │   │       └─ _is_claimed: 是否为声明消息 (PENDING恢复)
    │   │   │
    │   │   └─ ✅ **新机制**: retry任务直接从Redis Streams retry队列获取
    │   │
    │   ├─ 🎯 分配任务到后端 (_assign_tasks_to_backends) ✅ (第577-620行)
    │   │   ├─ 🔑 使用IntelligentLoadBalancer.get_optimal_backend() 智能选择 ✅
    │   │   │   ├─ 任务数据格式转换：task_data_for_lb
    │   │   │   │   ├─ task_id: 任务ID
    │   │   │   │   ├─ url: 目标URL
    │   │   │   │   ├─ priority: 优先级字符串
    │   │   │   │   └─ config: JSON序列化的配置
    │   │   │   ├─ 智能选择调用：await self.load_balancer.get_optimal_backend(task_data_for_lb)
    │   │   │   ├─ 多因子评分算法：负载(40%) + 响应时间(30%) + 成功率(20%) + 类型匹配(10%)
    │   │   │   └─ 日志记录：✅ IntelligentLoadBalancer selected backend {backend_id} for task {task_id}
    │   │   │
    │   │   ├─ 🔄 备用方案：智能选择失败时使用简单轮询 ✅
    │   │   │   ├─ optimal_backend = available_backends[0] (选择第一个可用后端)
    │   │   │   ├─ available_backends = available_backends[1:] (移除已选择的后端)
    │   │   │   └─ 日志记录：🔄 Fallback to simple selection: backend {backend_id} for task {task_id}
    │   │   │
    │   │   ├─ 🚫 无可用后端处理：任务重新排队 ✅
    │   │   │   ├─ 调用：await self._requeue_task(task_data, priority, "No available backend after intelligent selection")
    │   │   │   └─ 日志记录：⚠️ Task {task_id} requeued due to no available backend
    │   │   │
    │   │   └─ 🚀 异步任务处理启动 ✅
    │   │       ├─ 创建异步任务：asyncio.create_task(self._process_task(task_id, task_data, optimal_backend, priority))
    │   │       └─ 日志记录：🚀 Task {task_id} assigned to backend {optimal_backend.id} (priority: {priority})
    │   │
    │   ├─ 🔄 循环控制
    │   │   ├─ 无任务时：await asyncio.sleep(0.1) 短暂等待
    │   │   └─ 异常处理：await asyncio.sleep(1) 错误恢复
    │   │
    │   └─ 📊 性能优化
    │       ├─ 非阻塞设计：所有操作使用异步
    │       ├─ 批量处理：一次获取多个任务
    │       └─ 智能等待：根据情况调整等待时间
    │
    ├─ 🔄 **直接重试机制** - 任务失败后立即处理 ✅ (已重构)
    │   ├─ 📊 重试计数检查：new_retry_count = retry_count + 1
    │   ├─ 🔄 重试条件判断：if new_retry_count <= max_retries
    │   ├─ 🎯 **直接持久化到Redis Streams retry队列**
    │   │   ├─ 优先级强制设置：priority = "retry" (不保留原优先级)
    │   │   ├─ 任务数据更新：retry_count、last_error、retry_at
    │   │   ├─ 内部字段清理：移除_message_id等XREADGROUP字段
    │   │   ├─ 复杂字段序列化：config、result转为JSON字符串
    │   │   └─ Redis入队：await self.redis.xadd(retry_queue_name, retry_task_data)
    │   ├─ 🚫 **永久失败处理**：retry_count > max_retries
    │   │   ├─ 生命周期追踪：track_task_failure(permanent_failure)
    │   │   ├─ 死信队列：move_to_dead_letter_queue()
    │   │   └─ 状态更新：status = "FAILED"
    │   └─ 🔑 **关键变更**：
    │       ├─ ✅ 每次处理retry任务时retry_count自动+1
    │       └─ ✅ retry任务统一优先级，不保留原始优先级
    │
    ├─ _health_monitor() - 健康监控 ✅ (第1037-1070行)
    │   ├─ 🔄 循环条件：while self.is_running
    │   ├─ 📊 系统健康状态收集
    │   │   ├─ worker_id: 调度器标识
    │   │   ├─ timestamp: 当前时间戳
    │   │   ├─ processing_tasks: len(self.processing_tasks) - 当前处理任务数
    │   │   ├─ processed_count: self.processed_count - 已处理任务计数
    │   │   ├─ failed_count: self.failed_count - 失败任务计数
    │   │   └─ uptime: time.time() - self.start_time - 运行时间
    │   ├─ 🚨 异常情况检测和告警
    │   │   ├─ processing_tasks > 100: 高并发任务告警
    │   ├─ 📡 健康流发布
    │   │   ├─ 流名称：scheduler_health_stream
    │   │   ├─ 最大长度：maxlen=500 (保留最近500条记录)
    │   │   └─ 发布方式：await self.redis.xadd()
    │   ├─ 📊 定期监控：await asyncio.sleep(30) - 每30秒检查一次
    │   └─ 🛡️ 异常处理：await asyncio.sleep(30) 错误恢复
    │
    └─ _heartbeat_loop() - 心跳循环 ✅ (第130-140行)
        ├─ 🔄 循环条件：while self.is_running
        ├─ 💓 Worker状态更新 (_update_heartbeat) (第142-165行)
        │   ├─ last_heartbeat: str(current_time) - 最后心跳时间戳
        │   ├─ processed_count: str(self.processed_count) - 已处理任务统计
        │   ├─ failed_count: str(self.failed_count) - 失败任务统计
        │   ├─ current_tasks: str(len(self.processing_tasks)) - 当前任务数
        │   └─ uptime: str(current_time - self.start_time) - 运行时间
        ├─ 💾 Redis状态存储
        │   ├─ 存储键：f"{REDIS_KEYS['worker_status']}{self.worker_id}"
        │   ├─ 存储方式：await self.redis.hset(mapping=worker_info)
        │   └─ 数据格式：Hash结构存储所有状态字段
        ├─ 📊 定期心跳：await asyncio.sleep(SYSTEM_CONFIG.get("worker_heartbeat_interval", 30))
        └─ 🛡️ 异常处理：await asyncio.sleep(5) 错误恢复
    ↓
7. 单任务处理流程 (_process_task) ✅
    ├─ 任务状态检查和验证 ✅
    │   ├─ 任务取消状态检查 (status == "CANCELLED")
    │   │   └─ 通过_get_task_status()获取任务状态并检查
    │   ├─ 后端可用性验证 (_is_backend_available) ✅
    │   │   ├─ 健康检查器验证 (load_balancer.health_checker.is_backend_healthy)
    │   │   └─ 简单连通性检查 (GET /health端点，5秒超时)
    │   └─ 处理记录管理 (self.processing_tasks) ✅
    │       ├─ 记录start_time, backend_id, priority
    │       ├─ 记录message_id, consumer_group, queue_name
    │       └─ 用于任务完成后的清理和负载释放
    │
    ├─ 生命周期追踪集成 ✅
    │   ├─ TaskLifecycleTracker.track_task_processing_start() - 任务开始处理
    │   │   ├─ 记录worker_id, backend_id, priority信息
    │   │   ├─ crawl4ai_job_id初始为None，稍后更新
    │   │   └─ 生命周期事件追踪
    │   └─ 任务状态更新 (_update_task_status) ✅
    │       ├─ status: "PROCESSING"
    │       ├─ backend_id: 分配的后端ID
    │       ├─ worker_id: 处理Worker ID
    │       ├─ priority: 任务优先级
    │       ├─ processing_started_at: 开始处理时间
    │       └─ crawl4ai_job_id: 提交成功后更新
    │
    ├─ 提交到Crawl4AI LLM后端 (_submit_to_crawl4ai) ✅
    │   ├─ 🔧 构建LLM任务请求数据
    │   │   ├─ request_data = {"urls": [task_data.get("url", "")], "config": task_data.get("config", {})}
    │   │   └─ 任务配置包含LLM查询指令和输出模式
    │   ├─ 🌐 HTTP POST请求：{backend.base_url}/llm/job ✅
    │   │   ├─ 使用aiohttp.ClientSession
    │   │   ├─ 30秒超时处理：aiohttp.ClientTimeout(total=30)
    │   │   └─ JSON格式请求体
    │   ├─ 📋 响应处理：检查response.status == 200
    │   ├─ 🆔 获取作业ID：result.get("job_id") → crawl4ai_job_id
    │   ├─ 🚫 错误处理：LLM job提交失败时返回None
    │   └─ 📝 日志记录：提交成功/失败状态
    │
    ├─ 结果轮询 (_poll_task_result) ✅
    │   ├─ ⏱️ 5分钟最大轮询时间：max_poll_time = 300秒
    │   ├─ 🔄 2秒轮询间隔：poll_interval = 2秒
    │   ├─ 🌐 状态查询：GET {backend.base_url}/llm/job/{crawl4ai_job_id} ✅
    │   │   ├─ 使用aiohttp.ClientSession
    │   │   ├─ 10秒查询超时：aiohttp.ClientTimeout(total=10)
    │   │   └─ JSON响应解析
    │   ├─ ✅ 完成处理：result.get("status") == "completed"
    │   │   └─ 调用_handle_task_completion()处理成功完成
    │   ├─ ❌ 失败处理：result.get("status") == "failed"
    │   │   └─ 调用_handle_task_failure()处理任务失败
    │   ├─ ⏰ 超时处理：轮询超时后调用_handle_task_failure()
    │   │   └─ 错误消息："Polling timeout"
    │   └─ 📊 处理时间计算：processing_time = time.time() - start_time
    │
    ├─ 任务完成处理 (_handle_task_completion) ✅
    │   ├─ 🔄 生命周期追踪：track_task_completion()
    │   │   ├─ 记录result数据、processing_time、worker_id
    │   │   └─ 更新任务生命周期状态
    │   ├─ 📊 任务状态更新：status = "COMPLETED"
    │   │   ├─ completed_at: 完成时间戳
    │   │   ├─ result: JSON序列化的结果数据
    │   │   └─ processing_time: 处理耗时
    │   ├─ 📈 后端性能指标更新
    │   │   └─ 调用_update_backend_metrics(backend_id, processing_time, success=True)
    │   └─ 📝 成功日志记录
    │
    ├─ 任务失败处理 (_handle_task_failure) ✅ - 🔧 已修复负载管理
    │   ├─ 🔑 显式后端负载释放 ✅ - 新增修复
    │   │   ├─ 方法签名：backend_id: Optional[str] = None
    │   │   ├─ 负载释放：await self.load_balancer.release_backend_load(backend_id)
    │   │   ├─ 错误处理：释放失败时记录错误日志
    │   │   └─ 调用位置：所有_handle_task_failure调用都传递backend.id
    │   ├─ 🔢 重试逻辑：new_retry_count = retry_count + 1
    │   ├─ 🔄 重试条件：new_retry_count <= max_retries
    │   │   ├─ 生命周期追踪：track_task_retry()
    │   │   ├─ 构建重试任务数据（priority强制设为"retry"）
    │   │   ├─ 直接持久化到Redis Streams retry队列
    │   │   └─ 更新任务状态为TaskStatus.RETRYING.value ("retrying")
    │   └─ 💀 永久失败处理：超过最大重试次数
    │       ├─ 生命周期追踪：track_task_failure()
    │       ├─ 移入死信队列：_move_to_dead_letter_queue()
    │       └─ 更新任务状态为TaskStatus.PERMANENTLY_FAILED.value ("permanently_failed")
    │
    └─ XREADGROUP消息确认 (_ack_message) ✅
        ├─ 成功完成后自动ACK
        ├─ 失败处理后安全ACK
        ├─ 异常处理后强制ACK
        ├─ 防止消息重复处理
        └─ 错误处理：ACK失败时记录错误日志
    ↓
8. 任务完成处理分支 ✅
    ├─ 成功完成 (_handle_task_completion) ✅
    │   ├─ 🔄 生命周期追踪：TaskLifecycleTracker.track_task_completion() ✅
    │   │   ├─ 记录任务结果数据：result.get("data", {})
    │   │   ├─ 记录处理时间：processing_time
    │   │   ├─ 记录worker_id：self.worker_id
    │   │   └─ 更新生命周期状态
    │   ├─ 📊 更新任务状态为COMPLETED：_update_task_status() ✅
    │   │   ├─ status: "COMPLETED"（字符串字面量）
    │   │   ├─ completed_at: time.time()
    │   │   ├─ result: json.dumps(result.get("data", {}))
    │   │   └─ processing_time: 处理耗时
    │   ├─ 📈 更新后端性能指标：_update_backend_metrics() ✅
    │   │   ├─ 调用load_balancer.update_backend_metrics()
    │   │   ├─ 记录响应时间 (response_time=processing_time)
    │   │   ├─ 记录成功状态 (success=True)
    │   │   └─ 更新后端统计信息
    │   ├─ 📢 自动发布完成事件：_publish_task_event() ✅
    │   │   ├─ 通过_update_task_status自动触发
    │   │   ├─ 事件类型：status值（"COMPLETED"）
    │   │   ├─ 发布到task_events:{task_id}流
    │   │   └─ 保留最近100个事件
    │   └─ 📝 日志记录：✅ Task {task_id} completed successfully in {processing_time:.2f}s
    │
    ├─ 任务失败 (_handle_task_failure) ✅ - 🔧 已修复负载管理
    │   ├─ 🔑 显式后端负载释放 ✅ - 新增修复
    │   │   ├─ 方法签名更新：backend_id: Optional[str] = None
    │   │   ├─ 负载释放逻辑：await self.load_balancer.release_backend_load(backend_id)
    │   │   ├─ 错误处理：释放失败时记录错误日志
    │   │   ├─ 调用位置修复：所有_handle_task_failure调用都传递backend.id
    │   │   └─ 解决问题：不再仅依赖finally块，显式处理负载释放
    │   ├─ 🔢 重试逻辑：new_retry_count = retry_count + 1 ✅
    │   │   ├─ 重试次数验证：int(task_data.get("retry_count", 0))
    │   │   └─ 最大重试限制：int(task_data.get("max_retries", SYSTEM_CONFIG.get("max_retries", 3)))
    │   ├─ 🔄 重试条件：new_retry_count <= max_retries ✅
    │   │   ├─ 生命周期追踪：track_task_retry()
    │   │   │   ├─ 记录重试次数：new_retry_count
    │   │   │   ├─ 延迟时间：delay=0（无延迟）
    │   │   │   ├─ 原始错误：original_error=error_message
    │   │   │   └─ worker_id：self.worker_id
    │   │   ├─ 🚀 直接持久化到Redis Streams retry队列 ✅
    │   │   │   ├─ 构建重试任务数据（priority强制设为"retry"）
    │   │   │   ├─ 移除内部字段（_message_id, _consumer_group等）
    │   │   │   ├─ 序列化复杂字段（config, result）
    │   │   │   └─ 调用redis.xadd(retry_queue_name, retry_task_data)
    │   │   ├─ 📊 更新任务状态：TaskStatus.RETRYING.value ("retrying") ✅
    │   │   │   ├─ retry_count: new_retry_count
    │   │   │   ├─ last_error: error_message
    │   │   │   ├─ priority: "retry"
    │   │   │   └─ requeued_to_retry_at: time.time()
    │   │   └─ 📝 重试日志：🔄 Task {task_id} added to retry queue (attempt {new_retry_count}/{max_retries})
    │   └─ ☠️ 超过重试限制 → 死信队列 ✅
    │       ├─ 生命周期追踪：TaskLifecycleTracker.track_task_failure()
    │       │   ├─ 记录永久失败状态：error_type="permanent_failure"
    │       │   ├─ 记录最终错误信息：error_message
    │       │   ├─ 记录重试历史：retry_count=new_retry_count-1, max_retries
    │       │   └─ 记录worker_id：self.worker_id
    │       ├─ 移入死信队列：_move_to_dead_letter_queue()
    │       │   ├─ 添加死信队列元数据
    │       │   ├─ moved_to_dlq_at: str(time.time())
    │       │   ├─ final_error: error_message
    │       │   ├─ worker_id和worker_type: "strict_priority_scheduler"
    │       │   └─ 发布到QUEUE_CONFIG["dead_letter"].name流
    │       ├─ 更新最终状态：TaskStatus.PERMANENTLY_FAILED.value ("permanently_failed") ✅
    │       │   ├─ failed_at: time.time()
    │       │   ├─ final_error: error_message
    │       │   └─ total_retries: new_retry_count - 1
    │       └─ 错误日志：❌ Task {task_id} failed permanently after {new_retry_count - 1} retries: {error_message}
    │
    ├─ ⚠️ 重要变更：延迟重试机制已废弃 ✅
    │   ├─ 🚫 不再使用DelayedTask和heapq延迟重试队列
    │   ├─ 🚫 不再使用指数退避延迟计算
    │   ├─ 🚫 不再使用_delayed_retry_processor后台处理器
    │   ├─ ✅ 改为直接持久化到Redis Streams retry队列
    │   ├─ ✅ 无延迟立即重试（delay=0）
    │   └─ ✅ 通过Redis Streams的消费者组机制保证可靠性
    │
    └─ 任务超时处理 ⚠️ 集成在轮询中
        ├─ 📍 实现位置：_poll_task_result方法中的超时处理逻辑
        ├─ ⏰ 超时检测：while time.time() - start_time < max_poll_time (300秒)
        ├─ 🚫 超时处理：调用_handle_task_failure(task_data, "Polling timeout", "high", backend.id)
        ├─ 🔄 生命周期追踪：通过_handle_task_failure集成
        ├─ 🔑 负载管理：超时时也会显式释放后端负载
        └─ 📝 注意：超时后会触发完整的失败处理流程（包括重试机制和负载释放）

## 🔧 重要修复：后端负载管理优化 ✅

### 修复前的问题 ❌
- `_handle_task_failure`方法不接收backend_id参数
- 任务失败时过度依赖finally块释放负载
- 某些失败路径可能遗漏负载释放
- 代码健壮性不足，存在潜在的负载泄漏风险

### 修复后的改进 ✅
- **方法签名优化**：`_handle_task_failure(task_data, error_message, priority, backend_id=None)`
- **显式负载释放**：在失败处理方法内部直接释放后端负载
- **全路径覆盖**：所有调用_handle_task_failure的地方都传递backend_id
- **错误处理增强**：负载释放失败时记录详细错误日志
- **双重保障机制**：显式释放 + finally块保障，确保负载不会泄漏

### 修复的调用位置 🔧
1. **任务提交失败**：`await self._handle_task_failure(task_data, "Failed to submit to Crawl4AI", priority, backend.id)`
2. **异常处理**：`await self._handle_task_failure(task_data, str(e), priority, backend.id)`
3. **轮询失败**：`await self._handle_task_failure(task_data, error_msg, "high", backend.id)`
4. **轮询超时**：`await self._handle_task_failure(task_data, "Polling timeout", "high", backend.id)`

### 技术影响 📊
- **可靠性提升**：消除了负载泄漏的潜在风险
- **资源管理优化**：确保后端负载计数的准确性
- **系统稳定性增强**：避免因负载计数错误导致的后端选择问题
- **监控能力改进**：负载释放失败时提供详细的错误日志

    ↓
9. IntelligentLoadBalancer 负载均衡 ✅
    ├─ 后端健康检查系统 (BackendHealthChecker) ✅
    │   ├─ is_backend_healthy() - 单个后端健康状态检查 ✅
    │   │   ├─ Redis健康状态查询：REDIS_KEYS['backend_health']{backend_id}
    │   │   ├─ 健康状态缓存机制：返回"1"(健康)或"0"(不健康)
    │   │   ├─ 异常处理：查询失败时返回False
    │   │   └─ 返回布尔值健康状态
    │   ├─ get_healthy_backends() - 获取所有健康后端 ✅
    │   │   ├─ 遍历所有配置的后端：self.backends.items()
    │   │   ├─ 逐个检查健康状态：await self.is_backend_healthy(backend_id)
    │   │   ├─ 过滤不健康后端：仅返回健康的Backend对象
    │   │   └─ 返回健康后端列表：List[Backend]
    │   ├─ _check_backend_health() - 实际健康检查 ✅
    │   │   ├─ HTTP GET请求：{backend.base_url}/health
    │   │   ├─ 10秒超时处理：aiohttp.ClientTimeout(total=10)
    │   │   ├─ 状态码200判断健康：response.status == 200
    │   │   ├─ 异常处理：ClientError和TimeoutError标记为不健康
    │   │   ├─ 更新Redis健康状态：backend_health:{backend_id}
    │   │   ├─ 设置健康状态过期时间：check_interval * 2
    │   │   └─ 记录健康检查时间：backend_last_check:{backend_id}
    │   ├─ start_health_check_loop() - 定期健康检查循环 ✅
    │   │   ├─ 检查间隔：SYSTEM_CONFIG["health_check_interval"]
    │   │   ├─ 并发检查所有后端：asyncio.gather(*tasks)
    │   │   ├─ 异常处理和重试机制：错误时5秒等待
    │   │   └─ 持续循环：while True无限循环
    │   ├─ mark_backend_unhealthy() - 手动标记后端不健康 ✅
    │   │   ├─ 设置健康状态为"0"
    │   │   ├─ 记录不健康原因：backend_unhealthy_reason:{backend_id}
    │   │   └─ 日志记录和异常处理
    │   └─ _check_all_backends() - 批量健康检查 ✅
    │       ├─ 为每个后端创建异步任务
    │       ├─ 并发执行所有检查任务
    │       └─ 异常安全：return_exceptions=True
    │
    ├─ 获取最优后端 (get_optimal_backend) ✅
    │   ├─ 健康后端过滤：await self.health_checker.get_healthy_backends()
    │   ├─ 空健康后端处理：返回None并记录警告日志
    │   ├─ 多因子评分计算：await self._calculate_backend_score() ✅
    │   │   ├─ 负载得分 (40%权重) ✅
    │   │   │   ├─ 当前负载查询：REDIS_KEYS['backend_load']{backend_id}
    │   │   │   ├─ 负载比率计算：int(current_load) / backend.max_capacity
    │   │   │   └─ 负载得分：max(0, 1 - load_ratio) * 0.4
    │   │   ├─ 响应时间得分 (30%权重) ✅
    │   │   │   ├─ 平均响应时间查询：REDIS_KEYS['backend_response_time']{backend_id}
    │   │   │   ├─ 默认值处理：未找到时使用"1000"毫秒
    │   │   │   └─ 响应得分：min(1, 1000 / float(avg_response_time)) * 0.3
    │   │   ├─ 成功率得分 (20%权重) ✅
    │   │   │   ├─ 成功率查询：REDIS_KEYS['backend_success_rate']{backend_id}
    │   │   │   ├─ 默认值处理：未找到时使用"0.95"
    │   │   │   └─ 成功得分：float(success_rate) * 0.2
    │   │   ├─ 任务类型匹配得分 (10%权重) ✅
    │   │   │   └─ _calculate_type_affinity() - 任务类型亲和性(当前返回1.0)
    │   │   ├─ 综合得分计算：load_score + response_score + success_score + type_score
    │   │   ├─ 详细日志记录：各项得分和总分
    │   │   └─ 异常处理：计算失败时返回0.0
    │   ├─ 最优后端选择：按综合得分排序，选择最高分 ✅
    │   │   ├─ 得分排序：scored_backends.sort(key=lambda x: x[1], reverse=True)
    │   │   └─ 选择最高分后端：scored_backends[0][0]
    │   └─ 原子性后端分配：await self._atomic_backend_assignment() ✅
    │       ├─ Lua脚本原子操作：容量检查和负载递增在同一事务
    │       ├─ 容量限制检查：current_load < max_capacity
    │       ├─ 负载计数器递增：INCR backend_load:{backend_id}
    │       ├─ 设置过期时间：EXPIRE 3600秒
    │       ├─ 分配失败时尝试备选后端：遍历其他得分后端
    │       └─ 异常处理和日志记录
    │
    ├─ 动态负载更新系统 ✅
    │   ├─ release_backend_load() - 释放后端负载 ✅
    │   │   ├─ Lua脚本原子操作：确保操作原子性
    │   │   ├─ 负载计数器递减：DECR backend_load:{backend_id}
    │   │   ├─ 防止负载计数器为负数：current_load > 0检查
    │   │   ├─ 返回值处理：成功释放返回1，无负载返回0
    │   │   └─ 异常处理和日志记录
    │   ├─ update_backend_metrics() - 更新性能指标 ✅
    │   │   ├─ 响应时间更新 (指数移动平均，权重0.1) ✅
    │   │   │   ├─ 获取当前平均值：REDIS_KEYS['backend_response_time']{backend_id}
    │   │   │   ├─ 计算新平均值：0.9 * current_avg + 0.1 * response_time
    │   │   │   ├─ 首次记录处理：current_avg为空时直接使用response_time
    │   │   │   └─ 设置过期时间：3600秒
    │   │   ├─ 成功率更新 (滑动窗口，最近100次请求) ✅
    │   │   │   ├─ Redis列表维护：backend_success_history:{backend_id}
    │   │   │   ├─ 新状态添加：LPUSH "1"(成功)或"0"(失败)
    │   │   │   ├─ 列表修剪：LTRIM保留最近100条记录
    │   │   │   ├─ 成功率计算：success_count / total_count
    │   │   │   ├─ 更新成功率：backend_success_rate:{backend_id}
    │   │   │   └─ 设置过期时间：3600秒
    │   │   ├─ 时间戳记录：current_time = time.time()
    │   │   └─ 异常处理：记录错误日志
    │   └─ get_backend_stats() - 获取后端统计信息 ✅
    │       ├─ 遍历所有后端：self.backends.items()
    │       ├─ 当前负载查询：backend_load:{backend_id}
    │       ├─ 利用率计算：current_load / max_capacity
    │       ├─ 平均响应时间：backend_response_time:{backend_id}
    │       ├─ 成功率查询：backend_success_rate:{backend_id}
    │       ├─ 健康状态检查：health_checker.is_backend_healthy()
    │       ├─ 统计信息组装：包含所有性能指标和基础信息
    │       └─ 异常处理：错误时返回error字段和is_healthy=False
    │
    └─ 原子性操作保障 ✅
        ├─ _atomic_backend_assignment() - 原子性后端分配 ✅
        │   ├─ Lua脚本实现：确保容量检查和负载递增的原子性
        │   ├─ 脚本逻辑：检查current_load < max_capacity后执行INCR
        │   ├─ 返回值：成功分配返回1，容量不足返回0
        │   ├─ 过期时间设置：EXPIRE backend_key 3600
        │   └─ 异常处理：脚本执行失败时返回False
        ├─ release_backend_load() Lua脚本 ✅
        │   ├─ 原子性负载释放：检查current_load > 0后执行DECR
        │   ├─ 防止负数：避免负载计数器变为负值
        │   └─ 返回状态：释放成功返回1，无负载返回0
        └─ Redis键过期管理 ✅
            ├─ 负载计数器：backend_load:{backend_id} - 3600秒过期
            ├─ 性能指标：backend_response_time和backend_success_rate - 3600秒过期
            ├─ 健康状态：backend_health:{backend_id} - check_interval * 2过期
            ├─ 成功历史：backend_success_history:{backend_id} - 3600秒过期
            └─ 健康检查时间：backend_last_check:{backend_id} - 3600秒过期
    ↓
10. 监控和事件发布系统 ✅
    ├─ Worker注册管理 ✅
    │   ├─ 基础Worker状态 (worker_status:{worker_id}) ✅
    │   │   ├─ processed_count: 已处理任务数
    │   │   ├─ failed_count: 失败任务数  
    │   │   ├─ current_tasks: 当前处理任务数
    │   │   ├─ uptime: Worker运行时间
    │   │   ├─ last_heartbeat: 最后心跳时间
    │   │   └─ health_status: 健康状态
    │   │
    │   ├─ 增强Worker注册 (enhanced_worker_registry:{worker_id}) ✅
    │   │   ├─ Worker能力注册: supported_task_types, max_concurrent_tasks
    │   │   ├─ 负载均衡权重: load_balancing_weight, priority_handling
    │   │   ├─ 性能指标历史: performance_metrics, throughput_history
    │   │   └─ 故障恢复信息: failure_recovery_config, health_check_config
    │   │
    │   └─ 活跃Worker集合 (active_workers) ✅
    │       ├─ 动态添加/移除: sadd/srem操作管理Worker上线下线
    │       ├─ 心跳超时检测: 3倍心跳间隔超时检测 (worker_heartbeat_interval * 3)
    │       ├─ 健康状态监控: 实时健康状态检查和清理
    │       └─ 负载分布统计: Worker负载分布分析和统计
    │
    ├─ 实时监控流 ✅
    │   ├─ worker_heartbeat_stream - Worker心跳流 ✅
    │   │   ├─ 基础心跳信息: worker_id, timestamp, status, health_status
    │   │   ├─ 性能指标: processed_count, failed_count, current_tasks, uptime
    │   │   ├─ 系统资源: memory_usage, cpu_usage (通过psutil获取)
    │   │   ├─ 任务统计: success_rate, throughput (tasks/sec), avg_response_time
    │   │   └─ 增强心跳: enhanced_heartbeat包含完整Worker状态信息
    │   │
    │   ├─ global_task_events - 全局任务事件流 ✅
    │   │   ├─ 任务生命周期事件: TASK_CREATED, TASK_QUEUED, TASK_PROCESSING_STARTED, TASK_COMPLETED, TASK_FAILED
    │   │   ├─ 重试事件: TASK_RETRY_SCHEDULED, TASK_RECOVERED, TASK_TIMEOUT
    │   │   ├─ 消息确认事件: MESSAGE_ACKNOWLEDGED (XREADGROUP ACK追踪)
    │   │   ├─ 系统事件: worker_registered, backend_health_changed
    │   │   └─ 事件数据: task_id, event_type, timestamp, source, data (JSON)
    │   │
    │   ├─ scheduler_performance_metrics - 调度器性能流 ✅
    │   │   ├─ 队列统计: queue_metrics包含各队列长度和消费者组信息
    │   │   ├─ 系统指标: timestamp, collector标识, metrics_data (JSON)
    │   │   ├─ 性能分析: 通过MonitoringStatistics收集和分析
    │   │   ├─ 资源利用: active_workers统计, processing_tasks统计
    │   │   └─ 健康检查: 系统健康状态和性能趋势分析
    │   │
    │   ├─ scheduler_health_stream - 调度器健康状态流 ✅
    │   │   ├─ 系统状态: scheduler_status, uptime, health_data
    │   │   ├─ 资源状态: memory_usage, cpu_usage (通过psutil)
    │   │   ├─ 队列健康: queue_health, consumer_group_status
    │   │   ├─ 组件状态: load_balancer_health, worker_health
    │   │   └─ 告警状态: system_alerts_stream集成告警系统
    │   │
    │   ├─ 指标存储流 (metrics:{metric_name}) ✅
    │   │   ├─ 时序数据: 使用Redis Streams存储指标时序数据
    │   │   ├─ 最新值缓存: metrics:latest:{metric_name}存储最新指标值
    │   │   ├─ 系统指标: system.uptime, system.active_workers, system.success_rate
    │   │   ├─ 队列指标: queue.{type}.length, queue.{type}.pending, queue.{type}.utilization
    │   │   ├─ Worker指标: worker.{id}.processed, worker.{id}.success_rate, worker.{id}.throughput
    │   │   └─ 后端指标: backend.{id}.load, backend.{id}.response_time, backend.{id}.success_rate
    │   │
    │   ├─ 告警流 (alerts:recent, alert:{alert_id}) ✅
    │   │   ├─ 告警记录: alert_id, level, title, message, timestamp, source, metadata
    │   │   ├─ 告警级别: INFO, WARNING, ERROR, CRITICAL
    │   │   ├─ 告警规则: high_queue_length, low_success_rate, no_active_workers
    │   │   ├─ 告警处理: 多个告警处理器支持，日志记录和通知
    │   │   └─ 告警去重: 冷却时间机制防止重复告警
    │   │
    │   └─ task_events:{task_id} - 任务级事件流 ✅
    │       ├─ 生命周期事件: 任务创建、排队、处理、完成全流程追踪
    │       ├─ 状态转换: task_transitions:{task_id}记录状态变更历史
    │       ├─ 错误事件: 错误类型、错误消息、重试信息
    │       ├─ 性能事件: 处理时间、等待时间、响应时间
    │       └─ 上下文信息: 批次ID、Worker ID、后端ID、crawl4ai_job_id
    │
    └─ 任务生命周期追踪 (TaskLifecycleTracker) ✅
        ├─ track_task_creation() - 任务创建追踪 ✅
        │   ├─ 记录任务创建事件 (TASK_CREATED)
        │   ├─ 初始状态转换: NONE -> PENDING
        │   ├─ 存储创建时间和批次信息
        │   └─ 更新任务生命周期状态
        ├─ track_task_queued() - 任务排队追踪 ✅
        │   ├─ 记录排队事件 (TASK_QUEUED)
        │   ├─ 状态转换: PENDING -> QUEUED
        │   ├─ 记录队列名称、优先级、message_id
        │   └─ 更新排队时间和队列信息
        ├─ track_task_processing_start() - 任务开始处理追踪 ✅
        │   ├─ 记录处理开始事件 (TASK_PROCESSING_STARTED)
        │   ├─ 状态转换: QUEUED -> PROCESSING
        │   ├─ 绑定worker_id、backend_id、crawl4ai_job_id
        │   └─ 记录处理开始时间
        ├─ track_task_completion() - 任务完成追踪 ✅
        │   ├─ 记录完成事件 (TASK_COMPLETED)
        │   ├─ 状态转换: PROCESSING -> COMPLETED
        │   ├─ 计算处理耗时和结果大小
        │   └─ 更新成功统计
        ├─ track_task_failure() - 任务失败追踪 ✅
        │   ├─ 记录失败事件 (TASK_FAILED)
        │   ├─ 状态转换: PROCESSING -> FAILED/PERMANENTLY_FAILED
        │   ├─ 记录错误信息、错误类型、重试次数
        │   └─ 区分可重试失败和永久失败
        ├─ track_task_retry() - 任务重试追踪 ✅
        │   ├─ 记录重试事件 (TASK_RETRY_SCHEDULED)
        │   ├─ 状态转换: FAILED -> RETRYING
        │   ├─ 记录重试次数、延迟时间、原始错误
        │   └─ 计算下次重试时间
        ├─ track_task_timeout() - 任务超时追踪 ✅
        │   ├─ 记录超时事件 (TASK_TIMEOUT)
        │   ├─ 状态转换: PROCESSING -> TIMEOUT
        │   ├─ 记录超时时长和Worker信息
        │   └─ 更新超时统计
        ├─ track_task_recovery() - 任务恢复追踪 ✅
        │   ├─ 记录恢复事件 (TASK_RECOVERED)
        │   ├─ 状态转换: STUCK -> STUCK_RECOVERED
        │   ├─ 记录恢复原因和Worker信息
        │   └─ 更新恢复统计
        ├─ track_message_acknowledgment() - 消息确认追踪 ✅
        │   ├─ 记录ACK事件 (MESSAGE_ACKNOWLEDGED)
        │   ├─ 记录message_id、queue_name、consumer_group
        │   ├─ 更新任务消息确认状态
        │   └─ 防止消息重复处理
        └─ get_task_lifecycle_history() - 获取任务生命周期历史 ✅
            ├─ 从task_transitions:{task_id}获取状态转换历史
            ├─ 从task_events:{task_id}获取生命周期事件
            ├─ 获取当前任务状态信息
            └─ 返回完整的任务生命周期数据
    ↓
11. 数据存储和状态管理 ✅
    ├─ Redis数据结构 ✅
    │   ├─ 任务状态存储 (task:{task_id}) - HASH结构 ✅
    │   │   ├─ 基础信息: task_id, url, status, priority
    │   │   ├─ 时间戳: created_at, queued_at, processing_started_at, completed_at
    │   │   ├─ 配置信息: config (JSON), max_retries, retry_count
    │   │   ├─ 处理信息: backend_id, worker_id, crawl4ai_job_id
    │   │   ├─ 结果数据: result (JSON), error, processing_time
    │   │   ├─ 批次信息: batch_id, batch_index
    │   │   ├─ 消息确认: message_acknowledged, ack_timestamp, ack_message_id
    │   │   └─ 生命周期状态: updated_at, success标志
    │   │
    │   ├─ 队列系统 (Redis Streams) ✅
    │   │   ├─ queue:priority:high - 高优先级队列 (maxlen=1000, ttl=7200s)
    │   │   ├─ queue:priority:medium - 中优先级队列 (maxlen=5000, ttl=3600s)
    │   │   ├─ queue:priority:low - 低优先级队列 (maxlen=10000, ttl=1800s)
    │   │   ├─ queue:retry - 重试队列 (maxlen=2000, ttl=86400s)
    │   │   ├─ queue:dead_letter - 死信队列 (maxlen=1000, ttl=604800s)
    │   │   ├─ queue:batch - 批次队列 (maxlen=2000, ttl=3600s)
    │   │   └─ queue:delayed - 延迟队列 (maxlen=5000, ttl=86400s)
    │   │
    │   ├─ 消费者组 (XREADGROUP) ✅
    │   │   ├─ high_priority_workers - 高优先级消费者组 (prefetch=1)
    │   │   ├─ medium_priority_workers - 中优先级消费者组 (prefetch=3)
    │   │   ├─ low_priority_workers - 低优先级消费者组 (prefetch=5)
    │   │   ├─ retry_workers - 重试消费者组 (prefetch=1)
    │   │   ├─ dlq_workers - 死信队列消费者组 (prefetch=1)
    │   │   ├─ batch_workers - 批次消费者组 (prefetch=10)
    │   │   └─ delayed_workers - 延迟消费者组 (prefetch=2)
    │   │
    │   ├─ Worker状态管理 ✅
    │   │   ├─ worker_status:{worker_id} - HASH结构 ✅
    │   │   │   ├─ 基础状态: worker_id, worker_type, status, health_status
    │   │   │   ├─ 统计信息: processed_count, failed_count, current_tasks
    │   │   │   ├─ 时间信息: start_time, last_heartbeat, uptime
    │   │   │   └─ 能力信息: xreadgroup_enabled, consumer_groups
    │   │   ├─ enhanced_worker_registry:{worker_id} - HASH结构 ✅
    │   │   │   ├─ 能力配置: supported_task_types, max_concurrent_tasks
    │   │   │   ├─ 负载均衡: load_balancing_weight, priority_handling
    │   │   │   ├─ 性能指标: performance_metrics, throughput_history
    │   │   │   └─ 故障恢复: failure_recovery_config, health_check_config
    │   │   └─ active_workers - SET结构 ✅
    │   │       ├─ 动态添加/移除: sadd/srem操作管理Worker上线下线
    │   │       ├─ 心跳超时检测: 3倍心跳间隔超时检测
    │   │       └─ 健康状态监控: 实时健康状态检查和清理
    │   │
    │   ├─ 后端负载管理 ✅
    │   │   ├─ backend_load:{backend_id} - STRING结构 ✅
    │   │   │   ├─ 当前负载计数 (原子性递增/递减)
    │   │   │   ├─ Lua脚本原子操作: 分配和释放负载
    │   │   │   └─ 3600秒自动过期时间
    │   │   ├─ backend_health:{backend_id} - STRING结构 ✅
    │   │   │   ├─ 健康状态: "1"(健康) / "0"(不健康)
    │   │   │   └─ 2倍健康检查间隔过期时间 (120s)
    │   │   ├─ backend_response_time:{backend_id} - STRING结构 ✅
    │   │   │   ├─ 平均响应时间 (毫秒)
    │   │   │   ├─ 指数移动平均算法更新
    │   │   │   └─ 3600秒过期时间
    │   │   ├─ backend_success_rate:{backend_id} - STRING结构 ✅
    │   │   │   ├─ 成功率 (0.0-1.0)
    │   │   │   ├─ 滑动窗口算法计算
    │   │   │   └─ 3600秒过期时间
    │   │   └─ backend_success_history:{backend_id} - LIST结构 ✅
    │   │       ├─ 最近100次请求成功状态记录
    │   │       ├─ 用于计算滑动窗口成功率
    │   │       └─ 自动维护列表长度限制
    │   │
    │   ├─ 批次管理 ✅
    │   │   ├─ batch:{batch_id} - HASH结构 ✅
    │   │   │   ├─ 基础信息: batch_id, status, total_tasks, completed_tasks
    │   │   │   ├─ 时间信息: created_at, started_at, completed_at
    │   │   │   ├─ 统计信息: success_count, failed_count, retry_count
    │   │   │   └─ 配置信息: priority, max_retries, timeout
    │   │   ├─ batches:by_created - ZSET结构 ✅
    │   │   │   ├─ 按创建时间排序的批次索引
    │   │   │   └─ 用于时间范围查询和清理
    │   │   └─ batches:by_status:{status} - SET结构 ✅
    │   │       ├─ 按状态分组的批次集合
    │   │       └─ 支持状态过滤查询
    │   │
    │   ├─ 系统监控指标 ✅
    │   │   ├─ system_metrics:status - HASH结构 ✅
    │   │   │   ├─ 系统状态: status, start_time, uptime
    │   │   │   ├─ 统计信息: total_tasks_processed, total_tasks_failed
    │   │   │   ├─ 组件状态: active_workers, healthy_backends
    │   │   │   └─ 最后更新: last_update, last_health_check
    │   │   ├─ system_metrics:config - HASH结构 ✅
    │   │   │   ├─ 系统配置参数存储
    │   │   │   └─ 运行时配置管理
    │   │   ├─ system_metrics:health - STRING结构 ✅
    │   │   │   ├─ 系统健康检查结果 (JSON)
    │   │   │   └─ 健康检查间隔过期
    │   │   └─ queue_metrics:{queue_name} - HASH结构 ✅
    │   │       ├─ 队列统计: total_messages, processed_messages
    │   │       ├─ 性能指标: avg_processing_time, throughput
    │   │       └─ 清理统计: cleaned_messages, last_cleanup
    │   │
    │   └─ 监控流系统 (Redis Streams) ✅
    │       ├─ worker_heartbeat_stream - Worker心跳流 (maxlen=1000)
    │       ├─ global_task_events - 全局任务事件流 (maxlen=10000)
    │       ├─ scheduler_performance_metrics - 调度器性能流 (maxlen=1000)
    │       ├─ scheduler_health_stream - 调度器健康流 (maxlen=500)
    │       ├─ system_alerts_stream - 系统告警流 (maxlen=1000)
    │       ├─ task_events:{task_id} - 任务事件流 (maxlen=100)
    │       ├─ task_transitions:{task_id} - 任务状态转换流 (maxlen=50)
    │       └─ metrics:{metric_name} - 指标时序流 (maxlen=1000)
    │
    ├─ 数据一致性保障 ✅
    │   ├─ Lua脚本原子操作 ✅
    │   │   ├─ 后端负载分配脚本 ✅
    │   │   │   ├─ 检查最大容量限制
    │   │   │   ├─ 原子性递增负载计数
    │   │   │   ├─ 设置过期时间 (3600s)
    │   │   │   └─ 返回分配结果
    │   │   ├─ 后端负载释放脚本 ✅
    │   │   │   ├─ 原子性递减负载计数
    │   │   │   ├─ 防止负数计数
    │   │   │   └─ 维护过期时间
    │   │   ├─ 批次进度更新脚本 ✅
    │   │   │   ├─ 原子性更新任务计数
    │   │   │   ├─ 计算完成百分比
    │   │   │   ├─ 更新批次状态
    │   │   │   └─ 返回进度信息
    │   │   └─ 任务状态查询脚本 ✅
    │   │       ├─ 高效批量查询任务状态
    │   │       ├─ 减少网络往返次数
    │   │       └─ 提高查询性能
    │   ├─ XREADGROUP消息确认机制 ✅
    │   │   ├─ 消息处理完成后自动ACK
    │   │   ├─ 异常处理后强制ACK (防止消息重复处理)
    │   │   ├─ PENDING消息恢复机制
    │   │   └─ 消息确认状态追踪 (MESSAGE_ACKNOWLEDGED事件)
    │   └─ 事务性操作 ✅
    │       ├─ 任务状态和事件同步更新
    │       ├─ 批次进度原子性更新
    │       ├─ Worker状态一致性维护
    │       └─ 后端健康状态同步更新
    │
    └─ 数据过期和清理机制 ✅
        ├─ 自动过期机制 ✅
        │   ├─ 后端负载计数: 3600秒过期 (1小时)
        │   ├─ 后端性能指标: 3600秒过期 (1小时)
        │   ├─ 后端健康状态: 120秒过期 (2倍健康检查间隔)
        │   ├─ Worker心跳: 90秒过期 (3倍心跳间隔)
        │   ├─ 系统健康检查: 健康检查间隔过期
        │   └─ 指标缓存: metrics:latest:{name} 3600秒过期
        ├─ 流数据长度限制 (maxlen) ✅
        │   ├─ 任务事件流: maxlen=100 (每个任务)
        │   ├─ 任务状态转换流: maxlen=50 (每个任务)
        │   ├─ Worker心跳流: maxlen=1000 (全局)
        │   ├─ 全局任务事件流: maxlen=10000 (全局)
        │   ├─ 调度器性能流: maxlen=1000 (全局)
        │   ├─ 调度器健康流: maxlen=500 (全局)
        │   ├─ 系统告警流: maxlen=1000 (全局)
        │   └─ 指标时序流: maxlen=1000 (每个指标)
        ├─ RedisKeyManager TTL策略 ✅
        │   ├─ 任务数据: 7天 (task:*, task:*:result)
        │   ├─ 任务指标: 3天 (task:*:metrics)
        │   ├─ 任务错误: 30天 (task:*:error)
        │   ├─ 批次数据: 30天 (batch:*)
        │   ├─ 日统计: 90天 (stats:daily:*)
        │   ├─ 小时统计: 7天 (stats:hourly:*)
        │   ├─ 锁数据: 300秒 (lock:*)
        │   └─ 全局统计: 永不过期
        └─ CleanupManager定期清理 ✅
            ├─ 过期任务数据清理 (retention_days配置)
            ├─ 空批次和孤儿任务检测清理
            ├─ 死信队列定期处理和清理
            ├─ 过期统计数据清理
            ├─ 清理操作日志记录 (logs:cleanup)
            ├─ 清理历史统计 (cleanup:history)
            └─ 清理性能监控和告警
    ↓
12. API响应返回 ✅
    ├─ QueueTaskSubmissionResponse (批量任务提交响应) ✅
    │   ├─ success: bool - 提交是否成功
    │   ├─ batch_id: str - 批次唯一标识
    │   ├─ task_ids: List[str] - 任务ID列表
    │   ├─ total_tasks: int - 总任务数
    │   ├─ valid_tasks: int - 有效任务数
    │   ├─ invalid_tasks: int - 无效任务数
    │   ├─ invalid_urls: List[str] - 无效URL列表 (default_factory=list)
    │   ├─ queue_info: Dict[str, Any] - 队列信息
    │   │   ├─ batch_id: 批次ID
    │   │   ├─ task_count: 任务数量
    │   │   ├─ invalid_urls: 无效URL列表
    │   │   ├─ priority: 任务优先级
    │   │   ├─ routing_strategy: "intelligent_load_balanced"
    │   │   └─ estimated_completion_time: 预计完成时间
    │   ├─ load_balancer_info: Dict[str, Any] - 负载均衡信息
    │   │   ├─ active_workers: 活跃Worker数量 (健康后端数量)
    │   │   ├─ total_capacity: 总容量 (所有后端最大容量之和)
    │   │   ├─ current_load: 当前负载 (所有后端当前负载之和)
    │   │   ├─ utilization: 利用率 (current_load / total_capacity)
    │   │   └─ backend_stats: 详细后端统计信息
    │   ├─ caller_info: Dict[str, Any] - 调用方追踪信息 ✅
    │   │   ├─ client_ip: 客户端IP (支持X-Forwarded-For和X-Real-IP)
    │   │   ├─ user_agent: 用户代理
    │   │   ├─ referer: 来源页面
    │   │   ├─ origin: 请求来源
    │   │   ├─ x_forwarded_for: X-Forwarded-For头部
    │   │   ├─ x_real_ip: X-Real-IP头部
    │   │   ├─ request_id: 请求唯一标识 (X-Request-ID或自动生成)
    │   │   ├─ session_id: 会话ID (X-Session-ID)
    │   │   └─ collected_at: 收集时间 (ISO格式)
    │   ├─ created_at: str - 创建时间戳 (ISO格式)
    │   └─ message: str - 响应消息
    │
    ├─ 错误响应处理 ✅
    │   ├─ ValidationException - 验证错误 (400) ✅
    │   │   ├─ error: "Validation failed"
    │   │   ├─ message: 具体错误信息
    │   │   ├─ details: 错误详情
    │   │   └─ error_code: "QUEUE_VALIDATION_ERROR"
    │   ├─ Internal Server Error - 内部错误 (500) ✅
    │   │   ├─ error: "Internal server error"
    │   │   ├─ message: "An unexpected error occurred while processing the queue task request"
    │   │   ├─ details: {"error_type": 异常类型名称}
    │   │   └─ error_code: "INTERNAL_ERROR"
    │   └─ 其他异常类型 ✅
    │       ├─ TaskNotFoundException (404) - 任务未找到
    │       ├─ ServiceUnavailableException (503) - 服务不可用
    │       ├─ RateLimitException (429) - 速率限制
    │       ├─ AuthenticationException (401) - 认证失败
    │       ├─ AuthorizationException (403) - 授权失败
    │       └─ RequestTooLargeException (413) - 请求过大
    │
    ├─ 请求验证规则 ✅
    │   ├─ URLs验证
    │   │   ├─ 不能为空: "URLs list cannot be empty"
    │   │   ├─ 最大数量: 1000个URL限制
    │   │   └─ URL格式验证: 支持http/https协议
    │   ├─ LLM查询验证
    │   │   ├─ 不能为空: "LLM query cannot be empty"
    │   │   └─ 长度限制: 最大10000字符
    │   └─ 批次名称验证: 最大100字符
    │
    └─ 响应时间和性能 ✅
        ├─ 立即响应策略: API调用后立即返回批次信息
        ├─ 异步处理: 任务处理在后台异步执行
        ├─ 状态查询: 通过/queue/status端点查询系统状态
        ├─ Worker状态: 通过/queue/workers端点查询Worker详情
        └─ 事件通知: 通过监控流实时跟踪任务状态
```

## 总结

Task-Middleware的 `/queue/tasks/batch` 端点实现了一个完整的现代化任务处理架构：

### 核心特性
1. **严格优先级调度**: HIGH → MEDIUM → LOW → RETRY 的绝对优先级处理
2. **XREADGROUP消费者组**: 确保消息可靠性和故障恢复
3. **智能负载均衡**: 多因子评分算法选择最优后端
4. **完整生命周期追踪**: 从任务创建到完成的全程监控
5. **原子性操作**: 使用Lua脚本确保数据一致性
6. **实时监控**: 多维度监控流和健康检查

### 架构优势
- **高可靠性**: XREADGROUP确保消息不丢失，PENDING消息恢复机制
- **高性能**: 严格优先级调度，智能负载均衡，异步并发处理
- **可观测性**: 完整的监控体系，实时状态追踪
- **可扩展性**: 支持多Worker、多后端的水平扩展
- **容错性**: 完善的重试机制、死信队列、故障恢复

### 处理流程
从API调用到任务完成，经历了12个主要阶段，每个阶段都有详细的错误处理和监控机制，确保了系统的稳定性和可靠性。

这个架构为大规模批量任务处理提供了强有力的技术支撑，同时通过完整的持久化机制和立即响应策略，确保了数据安全和用户体验的最佳平衡。

Task-Middleware的 `/queue/tasks/batch` 端点实现了一个完整的现代化任务处理架构：

### 核心特性
1. **严格优先级调度**: HIGH → MEDIUM → LOW → RETRY 的绝对优先级处理
2. **XREADGROUP消费者组**: 确保消息可靠性和故障恢复
3. **智能负载均衡**: 多因子评分算法选择最优后端
4. **完整生命周期追踪**: 从任务创建到完成的全程监控
5. **原子性操作**: 使用Lua脚本确保数据一致性
6. **实时监控**: 多维度监控流和健康检查

### 架构优势
- **高可靠性**: XREADGROUP确保消息不丢失，PENDING消息恢复机制
- **高性能**: 严格优先级调度，智能负载均衡，异步并发处理
- **可观测性**: 完整的监控体系，实时状态追踪
- **可扩展性**: 支持多Worker、多后端的水平扩展
- **容错性**: 完善的重试机制、死信队列、故障恢复

### 处理流程
从API调用到任务完成，经历了12个主要阶段，每个阶段都有详细的错误处理和监控机制，确保了系统的稳定性和可靠性。

这个架构为大规模批量任务处理提供了强有力的技术支撑，同时通过完整的持久化机制和立即响应策略，确保了数据安全和用户体验的最佳平衡。
