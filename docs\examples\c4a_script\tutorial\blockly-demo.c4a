# Demo: Login Flow with Blockly
# This script can be created visually using <PERSON><PERSON> blocks

GO https://example.com/login
WAIT `#login-form` 5

# Check if already logged in
IF (EXISTS `.user-avatar`) THEN GO https://example.com/dashboard

# Fill login form
CLICK `#email`
TYPE "<EMAIL>"
CLICK `#password`
TYPE "password123"

# Submit form
CLICK `button[type="submit"]`
WAIT `.dashboard` 10

# Success message
EVAL `console.log('Login successful!')`